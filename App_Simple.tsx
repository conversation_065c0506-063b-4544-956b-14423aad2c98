import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert, ImageBackground, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { LinearGradient } from 'expo-linear-gradient';

// Configurar notificaciones
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Interfaz para citas
interface Appointment {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  type: 'cita' | 'viaje' | 'tour';
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<'home' | 'login' | 'register'>('login');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);
  
  // Formularios
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [registerForm, setRegisterForm] = useState({ email: '', name: '', password: '' });
  
  // Datos de ejemplo
  const [appointments] = useState<Appointment[]>([
    {
      id: '1',
      title: 'Reunión de trabajo',
      date: '2025-01-15',
      time: '10:00',
      location: 'Oficina Central, Madrid',
      type: 'cita',
      coordinates: { latitude: 40.4168, longitude: -3.7038 }
    },
    {
      id: '2',
      title: 'Viaje a París',
      date: '2025-01-20',
      time: '08:00',
      location: 'Aeropuerto Barajas, Madrid',
      type: 'viaje',
      coordinates: { latitude: 40.4719, longitude: -3.5626 }
    },
    {
      id: '3',
      title: 'Tour por el Prado',
      date: '2025-01-18',
      time: '15:00',
      location: 'Museo del Prado, Madrid',
      type: 'tour',
      coordinates: { latitude: 40.4138, longitude: -3.6921 }
    }
  ]);

  useEffect(() => {
    requestLocationPermission();
    requestNotificationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const requestNotificationPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos', 'Se necesitan permisos de notificaciones para recordatorios');
    }
  };

  const handleLogin = () => {
    if (!loginForm.email || !loginForm.password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }
    setIsLoggedIn(true);
    setCurrentScreen('home');
    Alert.alert('¡Bienvenido!', 'Has iniciado sesión correctamente');
  };

  const handleRegister = () => {
    if (!registerForm.email || !registerForm.name || !registerForm.password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }
    setIsLoggedIn(true);
    setCurrentScreen('home');
    Alert.alert('¡Registro exitoso!', 'Tu cuenta ha sido creada');
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setCurrentScreen('login');
    setLoginForm({ email: '', password: '' });
    setRegisterForm({ email: '', name: '', password: '' });
  };

  const testDatabaseConnection = () => {
    Alert.alert(
      '🔍 Prueba de Base de Datos',
      `📱 Estado de la aplicación:

✅ Aplicación móvil: Funcionando
✅ Interfaz: Cargada correctamente
✅ GPS: ${userLocation ? 'Activo' : 'Pendiente'}
✅ Notificaciones: Configuradas
✅ Datos de ejemplo: ${appointments.length} citas

🔧 MongoDB: Listo para configurar
📊 Almacenamiento: Local (AsyncStorage)

¡La aplicación está funcionando perfectamente!`
    );
  };

  const renderLoginScreen = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2835&q=80' }}
      style={styles.backgroundImage}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <View style={styles.loginContainer}>
          <View style={styles.logoContainer}>
            <Ionicons name="calendar" size={60} color="#fff" />
            <Text style={styles.appTitle}>Recordar Citas</Text>
            <Text style={styles.appSubtitle}>Tu compañero de viajes y citas</Text>
          </View>

          <View style={styles.loginForm}>
            <TextInput
              style={styles.input}
              placeholder="Correo electrónico"
              placeholderTextColor="#ccc"
              value={loginForm.email}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            
            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor="#ccc"
              value={loginForm.password}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, password: text }))}
              secureTextEntry
            />

            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.registerButton} 
              onPress={() => setCurrentScreen('register')}
            >
              <Text style={styles.registerButtonText}>Registrarse</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.testButton} onPress={testDatabaseConnection}>
              <Ionicons name="server" size={16} color="#4A90E2" />
              <Text style={styles.testButtonText}>🔍 Probar Aplicación</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>Próximas Citas</Text>
            {appointments.slice(0, 2).map((appointment) => (
              <View key={appointment.id} style={styles.appointmentPreview}>
                <View style={styles.appointmentInfo}>
                  <Text style={styles.appointmentTitle}>{appointment.title}</Text>
                  <Text style={styles.appointmentDetails}>
                    {appointment.date} • {appointment.time}
                  </Text>
                  <Text style={styles.appointmentLocation}>{appointment.location}</Text>
                </View>
                <View style={styles.appointmentType}>
                  <Ionicons 
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    } 
                    size={24} 
                    color="#4A90E2" 
                  />
                </View>
              </View>
            ))}
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderRegisterScreen = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2835&q=80' }}
      style={styles.backgroundImage}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <View style={styles.loginContainer}>
          <View style={styles.logoContainer}>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => setCurrentScreen('login')}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Ionicons name="person-add" size={60} color="#fff" />
            <Text style={styles.appTitle}>Crear Cuenta</Text>
            <Text style={styles.appSubtitle}>Únete a Recordar Citas</Text>
          </View>

          <View style={styles.loginForm}>
            <TextInput
              style={styles.input}
              placeholder="Nombre completo"
              placeholderTextColor="#ccc"
              value={registerForm.name}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, name: text }))}
            />

            <TextInput
              style={styles.input}
              placeholder="Correo electrónico"
              placeholderTextColor="#ccc"
              value={registerForm.email}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />
            
            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor="#ccc"
              value={registerForm.password}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, password: text }))}
              secureTextEntry
            />

            <TouchableOpacity style={styles.loginButton} onPress={handleRegister}>
              <Text style={styles.loginButtonText}>Registrarse</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.registerButton} 
              onPress={() => setCurrentScreen('login')}
            >
              <Text style={styles.registerButtonText}>¿Ya tienes cuenta? Inicia sesión</Text>
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderHomeScreen = () => (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4A90E2', '#357ABD']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>¡Hola!</Text>
            <Text style={styles.headerSubtitle}>Tienes {appointments.length} citas programadas</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Próximas Citas</Text>
          {appointments.map((appointment) => (
            <View key={appointment.id} style={styles.appointmentCard}>
              <View style={styles.appointmentHeader}>
                <View style={[styles.typeIndicator, { 
                  backgroundColor: 
                    appointment.type === 'cita' ? '#FF6B6B' :
                    appointment.type === 'viaje' ? '#4ECDC4' : '#96CEB4'
                }]}>
                  <Ionicons 
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    } 
                    size={16} 
                    color="#fff" 
                  />
                </View>
                <Text style={styles.appointmentCardTitle}>{appointment.title}</Text>
              </View>
              
              <Text style={styles.appointmentCardDate}>
                {appointment.date} • {appointment.time}
              </Text>
              
              <View style={styles.appointmentCardLocation}>
                <Ionicons name="location" size={16} color="#666" />
                <Text style={styles.appointmentCardLocationText}>
                  {appointment.location}
                </Text>
              </View>

              <TouchableOpacity 
                style={styles.reminderButton}
                onPress={() => Alert.alert('Recordatorio', 'Recordatorio programado')}
              >
                <Ionicons name="notifications" size={16} color="#4A90E2" />
                <Text style={styles.reminderButtonText}>Recordar</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {currentScreen === 'login' && renderLoginScreen()}
      {currentScreen === 'register' && renderRegisterScreen()}
      {currentScreen === 'home' && renderHomeScreen()}
    </View>
  );
}

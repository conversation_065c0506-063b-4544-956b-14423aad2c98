# 🗄️ Configuración de MongoDB para Recordar Citas

## 🚀 Guía Rápida de Configuración

### 1. 📝 Crear Cuenta en MongoDB Atlas (GRATIS)

1. Ve a [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Haz clic en "Try Free"
3. Crea tu cuenta con email y contraseña
4. Verifica tu email

### 2. 🏗️ Crear tu Cluster (Base de Datos)

1. **Selecciona el plan gratuito:**
   - Shared Clusters → M0 Sandbox (FREE)
   - Región: Elige la más cercana a ti
   - Nombre del cluster: `recordar-citas`

2. **Espera a que se cree** (2-3 minutos)

### 3. 🔐 Configurar Acceso a la Base de Datos

1. **Crear usuario de base de datos:**
   - Ve a "Database Access" en el menú lateral
   - Clic en "Add New Database User"
   - Username: `recordar-citas`
   - Password: `recordar123` (o la que prefieras)
   - Database User Privileges: "Read and write to any database"
   - Clic en "Add User"

### 4. 🌐 Configurar Acceso de Red

1. **Permitir conexiones:**
   - Ve a "Network Access" en el menú lateral
   - Clic en "Add IP Address"
   - Selecciona "Allow access from anywhere" (0.0.0.0/0)
   - Clic en "Confirm"

### 5. 🔗 Obtener la Cadena de Conexión

1. **Conectar a tu aplicación:**
   - Ve a "Clusters" → Clic en "Connect"
   - Selecciona "Connect your application"
   - Driver: Node.js, Version: 4.1 or later
   - **Copia la connection string**

2. **Ejemplo de connection string:**
   ```
   mongodb+srv://recordar-citas:<password>@cluster0.xxxxx.mongodb.net/recordar-citas?retryWrites=true&w=majority
   ```

### 6. ⚙️ Configurar en la Aplicación

**Opción A: Editar directamente el archivo**
1. Abre `config/database.ts`
2. Reemplaza `MONGODB_URI` con tu connection string
3. Asegúrate de reemplazar `<password>` con tu contraseña real

**Opción B: Usar variables de entorno (Recomendado)**
1. Crea un archivo `.env` en la raíz del proyecto:
   ```
   EXPO_PUBLIC_MONGODB_URI=mongodb+srv://recordar-citas:<EMAIL>/recordar-citas?retryWrites=true&w=majority
   ```

## 🧪 Probar la Conexión

1. **En la aplicación:**
   - Abre la app en tu móvil
   - En la pantalla de login, toca "🔍 Probar MongoDB"
   - Verás el estado de la conexión

2. **Desde el código:**
   ```typescript
   import { getDatabaseStatus } from './config/database';
   console.log(getDatabaseStatus());
   ```

## 📊 Estructura de la Base de Datos

La aplicación creará automáticamente estas colecciones:

### 👥 Colección `users`
```json
{
  "_id": "ObjectId",
  "email": "<EMAIL>",
  "name": "Nombre Usuario",
  "password": "hash_sha256",
  "createdAt": "2025-01-13T...",
  "lastLogin": "2025-01-13T..."
}
```

### 📅 Colección `appointments`
```json
{
  "_id": "ObjectId",
  "userId": "ObjectId_del_usuario",
  "title": "Reunión de trabajo",
  "date": "2025-01-15",
  "time": "10:00",
  "location": "Oficina Central",
  "type": "cita|viaje|tour",
  "coordinates": {
    "latitude": 40.4168,
    "longitude": -3.7038
  },
  "description": "Descripción opcional",
  "reminderSet": true,
  "completed": false,
  "createdAt": "2025-01-13T...",
  "updatedAt": "2025-01-13T..."
}
```

## 🔒 Seguridad y Mejores Prácticas

### 🛡️ Seguridad Implementada
- ✅ Contraseñas hasheadas con SHA256
- ✅ Validación de datos de entrada
- ✅ Almacenamiento seguro local
- ✅ Conexión encriptada (SSL/TLS)

### 🔧 Configuración de Producción
Para una app en producción, considera:

1. **IP Whitelist específica** en lugar de "anywhere"
2. **Usuario de BD con permisos limitados**
3. **Variables de entorno** para credenciales
4. **Backup automático** de datos
5. **Monitoreo** de uso y rendimiento

## 🚨 Solución de Problemas

### ❌ Error: "Authentication failed"
- Verifica usuario y contraseña en Database Access
- Asegúrate de reemplazar `<password>` en la connection string

### ❌ Error: "Network timeout"
- Verifica configuración en Network Access
- Comprueba tu conexión a internet

### ❌ Error: "Database not found"
- MongoDB Atlas crea la BD automáticamente
- Verifica el nombre en la connection string

### 📱 Modo Offline
Si MongoDB no está disponible:
- ✅ La app funciona completamente offline
- ✅ Datos guardados en AsyncStorage
- ✅ Sincronización automática al reconectar

## 💡 Consejos Adicionales

### 🆓 Límites del Plan Gratuito
- **Almacenamiento:** 512 MB
- **Conexiones:** 500 simultáneas
- **Transferencia:** Sin límite

### 📈 Escalabilidad
Para crecer tu app:
1. **Upgrade a M2/M5** para más almacenamiento
2. **Implementar índices** para mejor rendimiento
3. **Usar MongoDB Realm** para sync en tiempo real
4. **Agregar analytics** con MongoDB Charts

### 🔄 Backup y Restauración
- MongoDB Atlas hace backups automáticos
- Puedes restaurar desde cualquier punto en el tiempo
- Exporta datos importantes regularmente

## 📞 Soporte

Si tienes problemas:
1. **Revisa la documentación** de MongoDB Atlas
2. **Usa el botón "Probar MongoDB"** en la app
3. **Consulta los logs** en la consola de Expo
4. **Contacta soporte** de MongoDB Atlas (24/7)

---

**¡Tu aplicación Recordar Citas está lista para usar MongoDB! 🎉**

# 📱 Recordar Citas - Aplicación Móvil

Una aplicación móvil completa para recordar citas, viajes y tours con funcionalidad GPS integrada, desarrollada con **Expo** y **React Native**.

## ✨ Características Principales

### 🔐 **Autenticación**
- Pantalla de login/registro con diseño atractivo
- Imagen de fondo de viajes para inspirar
- Vista previa de próximas citas en la pantalla de login

### 📅 **Gestión de Citas**
- **Citas médicas, laborales y personales**
- **Viajes** con información de vuelos y destinos
- **Tours** con puntos de interés y rutas
- Vista detallada de cada cita con información completa

### 🗺️ **Funcionalidad GPS**
- **Ubicación en tiempo real** del usuario
- **Cálculo de distancias** a las ubicaciones de citas
- **Navegación GPS** a destinos
- **Mapa interactivo** con marcadores de ubicaciones

### 🔔 **Recordatorios Inteligentes**
- **Notificaciones push** 30 minutos antes de cada cita
- **Recordatorios personalizables** para cada tipo de evento
- **Alertas de proximidad** cuando estés cerca del destino

### 📱 **Diseño Móvil Optimizado**
- **Interfaz nativa** optimizada para dispositivos móviles
- **Navegación por pestañas** intuitiva
- **Gestos táctiles** naturales
- **Diseño responsivo** para diferentes tamaños de pantalla

## 🚀 Instalación y Uso

### Prerrequisitos
- Node.js 18+ instalado
- Expo CLI instalado globalmente: `npm install -g @expo/cli`
- Expo Go app en tu dispositivo móvil ([Android](https://play.google.com/store/apps/details?id=host.exp.exponent) | [iOS](https://apps.apple.com/app/expo-go/id982107779))

### Instalación
```bash
# Clonar el repositorio
git clone <tu-repositorio>
cd recordar-citas

# Instalar dependencias
npm install

# Iniciar la aplicación
npm start
```

### Uso en Dispositivo Móvil
1. **Escanea el código QR** que aparece en la terminal con la app Expo Go
2. **Permite permisos** de ubicación y notificaciones cuando se soliciten
3. **Inicia sesión** en la pantalla principal
4. **Explora las funcionalidades**:
   - Ver citas en el dashboard principal
   - Navegar al mapa GPS para ver ubicaciones
   - Programar recordatorios para tus citas
   - Calcular distancias a tus destinos

## 📱 Pantallas de la Aplicación

### 🏠 **Pantalla Principal (Home)**
- Dashboard con resumen de citas
- Accesos rápidos a funciones principales
- Tarjetas de citas con información detallada
- Navegación inferior para cambiar entre secciones

### 📋 **Gestión de Citas**
- Lista completa de todas las citas
- Detalles expandidos de cada cita
- Botones de acción: recordar, navegar, editar
- Filtros por tipo de cita (cita, viaje, tour)

### 🗺️ **Mapa GPS**
- Visualización de ubicación actual
- Marcadores de todas las citas programadas
- Información de distancia a cada ubicación
- Botones de navegación directa

### 🔐 **Login/Registro**
- Pantalla de bienvenida con imagen de fondo
- Formularios de login y registro
- Vista previa de citas próximas
- Transición suave entre pantallas

## 🛠️ Tecnologías Utilizadas

### **Frontend Móvil**
- **Expo** - Plataforma de desarrollo móvil
- **React Native** - Framework para aplicaciones nativas
- **TypeScript** - Tipado estático para JavaScript
- **Expo Vector Icons** - Iconografía consistente

### **Funcionalidades Nativas**
- **expo-location** - Geolocalización y GPS
- **expo-notifications** - Notificaciones push locales
- **expo-linear-gradient** - Gradientes para UI atractiva
- **react-navigation** - Navegación entre pantallas

### **Características de Datos**
- **AsyncStorage** - Almacenamiento local persistente
- **expo-secure-store** - Almacenamiento seguro de credenciales
- **expo-calendar** - Integración con calendario del dispositivo

## 📊 Tipos de Citas Soportadas

### 🏥 **Citas Médicas**
- Consultas médicas
- Exámenes de laboratorio
- Terapias y tratamientos
- Recordatorios de medicamentos

### ✈️ **Viajes**
- Vuelos nacionales e internacionales
- Reservas de hotel
- Alquiler de vehículos
- Itinerarios de viaje

### 🗺️ **Tours y Actividades**
- Tours guiados por la ciudad
- Visitas a museos y atracciones
- Actividades al aire libre
- Eventos culturales

## 🔧 Configuración Avanzada

### Permisos Requeridos
```javascript
// Ubicación
await Location.requestForegroundPermissionsAsync();

// Notificaciones
await Notifications.requestPermissionsAsync();

// Calendario (opcional)
await Calendar.requestCalendarPermissionsAsync();
```

### Personalización de Notificaciones
```javascript
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});
```

## 🚀 Próximas Funcionalidades

- [ ] **Sincronización en la nube** con Firebase
- [ ] **Compartir citas** con otros usuarios
- [ ] **Integración con Google Calendar**
- [ ] **Modo offline** completo
- [ ] **Recordatorios por voz**
- [ ] **Análisis de patrones de viaje**
- [ ] **Integración con apps de transporte**
- [ ] **Exportar itinerarios a PDF**

## 📱 Instalación como PWA

Para instalar la aplicación como una app nativa en tu dispositivo:

1. **Android**: Abre la app en Expo Go y selecciona "Add to Home Screen"
2. **iOS**: Usa Safari para abrir la versión web y selecciona "Add to Home Screen"

## 🤝 Contribuir

¡Las contribuciones son bienvenidas! Por favor:

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 📞 Soporte

Si tienes alguna pregunta o problema:
- Abre un issue en GitHub
- Contacta al equipo de desarrollo
- Revisa la documentación de Expo

---

**¡Disfruta organizando tus citas y viajes con Recordar Citas! 🎉**

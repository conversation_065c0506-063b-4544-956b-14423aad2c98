import { useState } from 'react';
import { databaseService, User } from '../services/database';

interface AdminState {
  isLoading: boolean;
  error: string | null;
  users: User[];
}

interface AdminActions {
  activateUser: (email: string, days: number) => Promise<{ success: boolean; error?: string }>;
  deactivateUser: (email: string) => Promise<{ success: boolean; error?: string }>;
  extendSubscription: (email: string, days: number) => Promise<{ success: boolean; error?: string }>;
  getUserInfo: (email: string) => Promise<{ success: boolean; user?: User; error?: string }>;
  getAllUsers: () => Promise<void>;
  cleanupExpiredUsers: () => Promise<{ success: boolean; removedCount: number }>;
  calculateDaysRemaining: (user: User) => number;
  isUserExpired: (user: User) => boolean;
}

export const useAdmin = (): AdminState & AdminActions => {
  const [state, setState] = useState<AdminState>({
    isLoading: false,
    error: null,
    users: [],
  });

  const activateUser = async (email: string, days: number) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await databaseService.activateUser(email, days, 'admin');
      
      if (result.success) {
        // Actualizar lista de usuarios
        await getAllUsers();
      }
      
      setState(prev => ({ ...prev, isLoading: false, error: result.error || null }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al activar usuario' }));
      return { success: false, error: 'Error al activar usuario' };
    }
  };

  const deactivateUser = async (email: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await databaseService.deactivateUser(email);
      
      if (result.success) {
        // Actualizar lista de usuarios
        await getAllUsers();
      }
      
      setState(prev => ({ ...prev, isLoading: false, error: result.error || null }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al desactivar usuario' }));
      return { success: false, error: 'Error al desactivar usuario' };
    }
  };

  const extendSubscription = async (email: string, days: number) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await databaseService.extendUserSubscription(email, days);
      
      if (result.success) {
        // Actualizar lista de usuarios
        await getAllUsers();
      }
      
      setState(prev => ({ ...prev, isLoading: false, error: result.error || null }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al extender suscripción' }));
      return { success: false, error: 'Error al extender suscripción' };
    }
  };

  const getUserInfo = async (email: string) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await databaseService.getUserInfo(email);
      setState(prev => ({ ...prev, isLoading: false, error: result.error || null }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al obtener información del usuario' }));
      return { success: false, error: 'Error al obtener información del usuario' };
    }
  };

  const getAllUsers = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const users = await databaseService.getAllUsers();
      setState(prev => ({ ...prev, users, isLoading: false }));
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al cargar usuarios' }));
    }
  };

  const cleanupExpiredUsers = async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    
    try {
      const result = await databaseService.cleanupExpiredUsers();
      
      if (result.success) {
        // Actualizar lista de usuarios después de la limpieza
        await getAllUsers();
      }
      
      setState(prev => ({ ...prev, isLoading: false }));
      return result;
    } catch (error) {
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al limpiar usuarios expirados' }));
      return { success: false, removedCount: 0 };
    }
  };

  const calculateDaysRemaining = (user: User): number => {
    if (!user.expiresAt) return -1; // Sin expiración
    
    const now = new Date();
    const expiration = new Date(user.expiresAt);
    const diffTime = expiration.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  };

  const isUserExpired = (user: User): boolean => {
    if (!user.expiresAt) return false; // Sin expiración
    
    const now = new Date();
    const expiration = new Date(user.expiresAt);
    
    return now > expiration;
  };

  return {
    ...state,
    activateUser,
    deactivateUser,
    extendSubscription,
    getUserInfo,
    getAllUsers,
    cleanupExpiredUsers,
    calculateDaysRemaining,
    isUserExpired,
  };
};

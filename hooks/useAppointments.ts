import { useState, useEffect, useCallback } from 'react';
import { databaseService, Appointment } from '../services/database';

interface AppointmentsState {
  appointments: Appointment[];
  isLoading: boolean;
  error: string | null;
}

interface AppointmentsActions {
  loadAppointments: () => Promise<void>;
  addAppointment: (appointment: Omit<Appointment, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) => Promise<{ success: boolean; error?: string }>;
  updateAppointment: (id: string, updates: Partial<Appointment>) => Promise<{ success: boolean; error?: string }>;
  deleteAppointment: (id: string) => Promise<{ success: boolean; error?: string }>;
  getUpcomingAppointments: () => Appointment[];
  getAppointmentsByType: (type: 'cita' | 'viaje' | 'tour') => Appointment[];
  markAsCompleted: (id: string) => Promise<{ success: boolean; error?: string }>;
  setReminder: (id: string) => Promise<{ success: boolean; error?: string }>;
}

export const useAppointments = (): AppointmentsState & AppointmentsActions => {
  const [state, setState] = useState<AppointmentsState>({
    appointments: [],
    isLoading: false,
    error: null,
  });

  // Cargar citas al inicializar
  useEffect(() => {
    loadAppointments();
  }, []);

  const loadAppointments = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const appointments = await databaseService.getAppointments();
      
      // Ordenar por fecha y hora
      const sortedAppointments = appointments.sort((a, b) => {
        const dateA = new Date(`${a.date}T${a.time}`);
        const dateB = new Date(`${b.date}T${b.time}`);
        return dateA.getTime() - dateB.getTime();
      });
      
      setState({
        appointments: sortedAppointments,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      console.error('Error loading appointments:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Error al cargar las citas',
      }));
    }
  }, []);

  const addAppointment = useCallback(async (appointmentData: Omit<Appointment, '_id' | 'userId' | 'createdAt' | 'updatedAt'>) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await databaseService.saveAppointment(appointmentData);
      
      if (result.success && result.appointment) {
        setState(prev => ({
          ...prev,
          appointments: [...prev.appointments, result.appointment!].sort((a, b) => {
            const dateA = new Date(`${a.date}T${a.time}`);
            const dateB = new Date(`${b.date}T${b.time}`);
            return dateA.getTime() - dateB.getTime();
          }),
          isLoading: false,
        }));
        
        return { success: true };
      } else {
        setState(prev => ({ ...prev, isLoading: false, error: result.error }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error adding appointment:', error);
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al agregar la cita' }));
      return { success: false, error: 'Error al agregar la cita' };
    }
  }, []);

  const updateAppointment = useCallback(async (id: string, updates: Partial<Appointment>) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await databaseService.updateAppointment(id, updates);
      
      if (result.success) {
        setState(prev => ({
          ...prev,
          appointments: prev.appointments.map(apt => 
            apt._id === id ? { ...apt, ...updates, updatedAt: new Date() } : apt
          ),
          isLoading: false,
        }));
        
        return { success: true };
      } else {
        setState(prev => ({ ...prev, isLoading: false, error: result.error }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error updating appointment:', error);
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al actualizar la cita' }));
      return { success: false, error: 'Error al actualizar la cita' };
    }
  }, []);

  const deleteAppointment = useCallback(async (id: string) => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const result = await databaseService.deleteAppointment(id);
      
      if (result.success) {
        setState(prev => ({
          ...prev,
          appointments: prev.appointments.filter(apt => apt._id !== id),
          isLoading: false,
        }));
        
        return { success: true };
      } else {
        setState(prev => ({ ...prev, isLoading: false, error: result.error }));
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Error deleting appointment:', error);
      setState(prev => ({ ...prev, isLoading: false, error: 'Error al eliminar la cita' }));
      return { success: false, error: 'Error al eliminar la cita' };
    }
  }, []);

  const getUpcomingAppointments = useCallback(() => {
    const now = new Date();
    return state.appointments.filter(apt => {
      const appointmentDate = new Date(`${apt.date}T${apt.time}`);
      return appointmentDate > now && !apt.completed;
    }).slice(0, 5); // Próximas 5 citas
  }, [state.appointments]);

  const getAppointmentsByType = useCallback((type: 'cita' | 'viaje' | 'tour') => {
    return state.appointments.filter(apt => apt.type === type);
  }, [state.appointments]);

  const markAsCompleted = useCallback(async (id: string) => {
    return await updateAppointment(id, { completed: true });
  }, [updateAppointment]);

  const setReminder = useCallback(async (id: string) => {
    return await updateAppointment(id, { reminderSet: true });
  }, [updateAppointment]);

  return {
    ...state,
    loadAppointments,
    addAppointment,
    updateAppointment,
    deleteAppointment,
    getUpcomingAppointments,
    getAppointmentsByType,
    markAsCompleted,
    setReminder,
  };
};

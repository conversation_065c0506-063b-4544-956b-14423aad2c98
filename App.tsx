import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert, ImageBackground, TextInput, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from './hooks/useAuth';
import { useAppointments } from './hooks/useAppointments';
import { Appointment } from './services/database';

// Configurar notificaciones
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<'home' | 'login' | 'register' | 'appointments' | 'map' | 'add-appointment'>('login');
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);

  // Formularios de autenticación
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [registerForm, setRegisterForm] = useState({ email: '', name: '', password: '', confirmPassword: '' });

  // Hooks personalizados para MongoDB
  const { user, isLoading: authLoading, isAuthenticated, login, register, logout } = useAuth();
  const {
    appointments,
    isLoading: appointmentsLoading,
    error: appointmentsError,
    loadAppointments,
    addAppointment,
    updateAppointment,
    deleteAppointment,
    getUpcomingAppointments,
    setReminder
  } = useAppointments();

  // Determinar pantalla inicial basada en autenticación
  useEffect(() => {
    if (!authLoading) {
      setCurrentScreen(isAuthenticated ? 'home' : 'login');
    }
  }, [isAuthenticated, authLoading]);

  useEffect(() => {
    requestLocationPermission();
    requestNotificationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setUserLocation(location);
      } else {
        Alert.alert('Permisos', 'Se necesitan permisos de ubicación para usar el GPS');
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const requestNotificationPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos', 'Se necesitan permisos de notificaciones para recordatorios');
    }
  };

  const scheduleNotification = async (appointment: Appointment) => {
    const appointmentDate = new Date(`${appointment.date}T${appointment.time}:00`);
    const reminderTime = new Date(appointmentDate.getTime() - 30 * 60 * 1000); // 30 minutos antes

    await Notifications.scheduleNotificationAsync({
      content: {
        title: `Recordatorio: ${appointment.title}`,
        body: `Tu ${appointment.type} es en 30 minutos en ${appointment.location}`,
        sound: true,
      },
      trigger: reminderTime,
    });

    // Marcar recordatorio como configurado en la base de datos
    if (appointment._id) {
      await setReminder(appointment._id);
    }
  };

  const getDistanceToLocation = (coords: { latitude: number; longitude: number }) => {
    if (!userLocation) return 'Ubicación no disponible';

    // Función para calcular distancia usando la fórmula de Haversine
    const R = 6371; // Radio de la Tierra en km
    const dLat = (coords.latitude - userLocation.coords.latitude) * Math.PI / 180;
    const dLon = (coords.longitude - userLocation.coords.longitude) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(userLocation.coords.latitude * Math.PI / 180) * Math.cos(coords.latitude * Math.PI / 180) *
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    return distance < 1 ? `${Math.round(distance * 1000)}m` : `${distance.toFixed(1)}km`;
  };

  const handleLogin = async () => {
    if (!loginForm.email || !loginForm.password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    const result = await login(loginForm.email, loginForm.password);
    if (result.success) {
      setCurrentScreen('home');
      setLoginForm({ email: '', password: '' });
    } else {
      Alert.alert('Error', result.error || 'Error al iniciar sesión');
    }
  };

  const handleRegister = async () => {
    if (!registerForm.email || !registerForm.name || !registerForm.password || !registerForm.confirmPassword) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }

    if (registerForm.password !== registerForm.confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return;
    }

    if (registerForm.password.length < 6) {
      Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
      return;
    }

    const result = await register(registerForm.email, registerForm.name, registerForm.password);
    if (result.success) {
      setCurrentScreen('home');
      setRegisterForm({ email: '', name: '', password: '', confirmPassword: '' });
    } else {
      Alert.alert('Error', result.error || 'Error al registrar usuario');
    }
  };

  const handleLogout = async () => {
    await logout();
    setCurrentScreen('login');
  };

  const renderLoginScreen = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2835&q=80' }}
      style={styles.backgroundImage}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <View style={styles.loginContainer}>
          <View style={styles.logoContainer}>
            <Ionicons name="calendar" size={60} color="#fff" />
            <Text style={styles.appTitle}>Recordar Citas</Text>
            <Text style={styles.appSubtitle}>Tu compañero de viajes y citas</Text>
          </View>

          <View style={styles.loginForm}>
            <TextInput
              style={styles.input}
              placeholder="Correo electrónico"
              placeholderTextColor="#ccc"
              value={loginForm.email}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor="#ccc"
              value={loginForm.password}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, password: text }))}
              secureTextEntry
            />

            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleLogin}
              disabled={authLoading}
            >
              {authLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.registerButton}
              onPress={() => setCurrentScreen('register')}
            >
              <Text style={styles.registerButtonText}>Registrarse</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>Próximas Citas</Text>
            {getUpcomingAppointments().slice(0, 2).map((appointment) => (
              <View key={appointment._id} style={styles.appointmentPreview}>
                <View style={styles.appointmentInfo}>
                  <Text style={styles.appointmentTitle}>{appointment.title}</Text>
                  <Text style={styles.appointmentDetails}>
                    {appointment.date} • {appointment.time}
                  </Text>
                  <Text style={styles.appointmentLocation}>{appointment.location}</Text>
                </View>
                <View style={styles.appointmentType}>
                  <Ionicons
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    }
                    size={24}
                    color="#4A90E2"
                  />
                </View>
              </View>
            ))}
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderRegisterScreen = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2835&q=80' }}
      style={styles.backgroundImage}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <View style={styles.loginContainer}>
          <View style={styles.logoContainer}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setCurrentScreen('login')}
            >
              <Ionicons name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
            <Ionicons name="person-add" size={60} color="#fff" />
            <Text style={styles.appTitle}>Crear Cuenta</Text>
            <Text style={styles.appSubtitle}>Únete a Recordar Citas</Text>
          </View>

          <View style={styles.loginForm}>
            <TextInput
              style={styles.input}
              placeholder="Nombre completo"
              placeholderTextColor="#ccc"
              value={registerForm.name}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, name: text }))}
            />

            <TextInput
              style={styles.input}
              placeholder="Correo electrónico"
              placeholderTextColor="#ccc"
              value={registerForm.email}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor="#ccc"
              value={registerForm.password}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, password: text }))}
              secureTextEntry
            />

            <TextInput
              style={styles.input}
              placeholder="Confirmar contraseña"
              placeholderTextColor="#ccc"
              value={registerForm.confirmPassword}
              onChangeText={(text) => setRegisterForm(prev => ({ ...prev, confirmPassword: text }))}
              secureTextEntry
            />

            <TouchableOpacity
              style={styles.loginButton}
              onPress={handleRegister}
              disabled={authLoading}
            >
              {authLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.loginButtonText}>Registrarse</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.registerButton}
              onPress={() => setCurrentScreen('login')}
            >
              <Text style={styles.registerButtonText}>¿Ya tienes cuenta? Inicia sesión</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>Características</Text>
            <View style={styles.featureItem}>
              <Ionicons name="location" size={20} color="#4A90E2" />
              <Text style={styles.featureText}>GPS integrado para navegación</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="notifications" size={20} color="#4A90E2" />
              <Text style={styles.featureText}>Recordatorios inteligentes</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="cloud" size={20} color="#4A90E2" />
              <Text style={styles.featureText}>Sincronización en la nube</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderHomeScreen = () => (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4A90E2', '#357ABD']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>¡Hola {user?.name}!</Text>
            <Text style={styles.headerSubtitle}>Tienes {appointments.length} citas programadas</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#FF6B6B' }]}
            onPress={() => setCurrentScreen('appointments')}
          >
            <Ionicons name="calendar" size={30} color="#fff" />
            <Text style={styles.actionButtonText}>Mis Citas</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#4ECDC4' }]}
            onPress={() => setCurrentScreen('map')}
          >
            <Ionicons name="map" size={30} color="#fff" />
            <Text style={styles.actionButtonText}>Mapa GPS</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#45B7D1' }]}
            onPress={() => setCurrentScreen('add-appointment')}
          >
            <Ionicons name="add" size={30} color="#fff" />
            <Text style={styles.actionButtonText}>Nueva Cita</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#96CEB4' }]}
            onPress={() => {
              loadAppointments();
              Alert.alert('Sincronizado', 'Datos actualizados desde la nube');
            }}
          >
            <Ionicons name="refresh" size={30} color="#fff" />
            <Text style={styles.actionButtonText}>Sincronizar</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Próximas Citas</Text>
          {appointmentsLoading ? (
            <ActivityIndicator size="large" color="#4A90E2" style={styles.loader} />
          ) : appointments.length === 0 ? (
            <View style={styles.emptyState}>
              <Ionicons name="calendar-outline" size={60} color="#ccc" />
              <Text style={styles.emptyStateText}>No tienes citas programadas</Text>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() => setCurrentScreen('add-appointment')}
              >
                <Text style={styles.addButtonText}>Agregar primera cita</Text>
              </TouchableOpacity>
            </View>
          ) : (
            getUpcomingAppointments().map((appointment) => (
              <TouchableOpacity
                key={appointment._id}
                style={styles.appointmentCard}
                onPress={() => {
                  if (appointment.coordinates) {
                    setCurrentScreen('map');
                  }
                }}
              >
              <View style={styles.appointmentHeader}>
                <View style={[styles.typeIndicator, {
                  backgroundColor:
                    appointment.type === 'cita' ? '#FF6B6B' :
                    appointment.type === 'viaje' ? '#4ECDC4' : '#96CEB4'
                }]}>
                  <Ionicons
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    }
                    size={16}
                    color="#fff"
                  />
                </View>
                <Text style={styles.appointmentCardTitle}>{appointment.title}</Text>
              </View>

              <Text style={styles.appointmentCardDate}>
                {appointment.date} • {appointment.time}
              </Text>

              <View style={styles.appointmentCardLocation}>
                <Ionicons name="location" size={16} color="#666" />
                <Text style={styles.appointmentCardLocationText}>
                  {appointment.location}
                </Text>
                {appointment.coordinates && (
                  <Text style={styles.distanceText}>
                    {getDistanceToLocation(appointment.coordinates)}
                  </Text>
                )}
              </View>

              <TouchableOpacity
                style={styles.reminderButton}
                onPress={() => {
                  scheduleNotification(appointment);
                  Alert.alert('Recordatorio', 'Recordatorio programado 30 minutos antes');
                }}
              >
                <Ionicons name="notifications" size={16} color="#4A90E2" />
                <Text style={styles.reminderButtonText}>Recordar</Text>
              </TouchableOpacity>
            </TouchableOpacity>
          )))}
        </View>
      </ScrollView>

      <View style={styles.bottomNav}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={() => setCurrentScreen('home')}
        >
          <Ionicons name="home" size={24} color="#4A90E2" />
          <Text style={styles.navButtonText}>Inicio</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.navButton}
          onPress={() => setCurrentScreen('appointments')}
        >
          <Ionicons name="calendar" size={24} color="#666" />
          <Text style={styles.navButtonText}>Citas</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.navButton}
          onPress={() => setCurrentScreen('map')}
        >
          <Ionicons name="map" size={24} color="#666" />
          <Text style={styles.navButtonText}>Mapa</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderAppointmentsScreen = () => (
    <View style={styles.container}>
      <View style={styles.screenHeader}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Ionicons name="arrow-back" size={24} color="#4A90E2" />
        </TouchableOpacity>
        <Text style={styles.screenTitle}>Mis Citas</Text>
        <TouchableOpacity onPress={() => Alert.alert('Próximamente', 'Agregar nueva cita')}>
          <Ionicons name="add" size={24} color="#4A90E2" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content}>
        {appointments.map((appointment) => (
          <View key={appointment.id} style={styles.fullAppointmentCard}>
            <View style={styles.appointmentHeader}>
              <View style={[styles.typeIndicator, {
                backgroundColor:
                  appointment.type === 'cita' ? '#FF6B6B' :
                  appointment.type === 'viaje' ? '#4ECDC4' : '#96CEB4'
              }]}>
                <Ionicons
                  name={
                    appointment.type === 'cita' ? 'calendar' :
                    appointment.type === 'viaje' ? 'airplane' : 'map'
                  }
                  size={20}
                  color="#fff"
                />
              </View>
              <View style={styles.appointmentMainInfo}>
                <Text style={styles.fullAppointmentTitle}>{appointment.title}</Text>
                <Text style={styles.appointmentType}>
                  {appointment.type.charAt(0).toUpperCase() + appointment.type.slice(1)}
                </Text>
              </View>
            </View>

            <View style={styles.appointmentDetails}>
              <View style={styles.detailRow}>
                <Ionicons name="calendar" size={16} color="#666" />
                <Text style={styles.detailText}>{appointment.date}</Text>
              </View>

              <View style={styles.detailRow}>
                <Ionicons name="time" size={16} color="#666" />
                <Text style={styles.detailText}>{appointment.time}</Text>
              </View>

              <View style={styles.detailRow}>
                <Ionicons name="location" size={16} color="#666" />
                <Text style={styles.detailText}>{appointment.location}</Text>
                {appointment.coordinates && (
                  <Text style={styles.distanceText}>
                    ({getDistanceToLocation(appointment.coordinates)})
                  </Text>
                )}
              </View>
            </View>

            <View style={styles.appointmentActions}>
              <TouchableOpacity
                style={styles.actionBtn}
                onPress={() => {
                  scheduleNotification(appointment);
                  Alert.alert('Recordatorio', 'Recordatorio programado');
                }}
              >
                <Ionicons name="notifications" size={16} color="#4A90E2" />
                <Text style={styles.actionBtnText}>Recordar</Text>
              </TouchableOpacity>

              {appointment.coordinates && (
                <TouchableOpacity
                  style={styles.actionBtn}
                  onPress={() => setCurrentScreen('map')}
                >
                  <Ionicons name="navigate" size={16} color="#4A90E2" />
                  <Text style={styles.actionBtnText}>Navegar</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity
                style={styles.actionBtn}
                onPress={() => Alert.alert('Próximamente', 'Editar cita')}
              >
                <Ionicons name="create" size={16} color="#4A90E2" />
                <Text style={styles.actionBtnText}>Editar</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </View>
  );

  const renderMapScreen = () => (
    <View style={styles.container}>
      <View style={styles.screenHeader}>
        <TouchableOpacity onPress={() => setCurrentScreen('home')}>
          <Ionicons name="arrow-back" size={24} color="#4A90E2" />
        </TouchableOpacity>
        <Text style={styles.screenTitle}>Mapa GPS</Text>
        <TouchableOpacity onPress={() => Alert.alert('GPS', 'Actualizando ubicación...')}>
          <Ionicons name="refresh" size={24} color="#4A90E2" />
        </TouchableOpacity>
      </View>

      <View style={styles.mapContainer}>
        <View style={styles.mapPlaceholder}>
          <Ionicons name="map" size={80} color="#ccc" />
          <Text style={styles.mapPlaceholderText}>Mapa GPS</Text>
          <Text style={styles.mapPlaceholderSubtext}>
            {userLocation
              ? `Lat: ${userLocation.coords.latitude.toFixed(4)}, Lng: ${userLocation.coords.longitude.toFixed(4)}`
              : 'Obteniendo ubicación...'
            }
          </Text>
        </View>
      </View>

      <View style={styles.mapInfo}>
        <Text style={styles.mapInfoTitle}>Ubicaciones de Citas</Text>
        {appointments.filter(apt => apt.coordinates).map((appointment) => (
          <TouchableOpacity
            key={appointment.id}
            style={styles.mapLocationItem}
            onPress={() => Alert.alert('Navegación', `Navegar a ${appointment.location}`)}
          >
            <View style={[styles.locationMarker, {
              backgroundColor:
                appointment.type === 'cita' ? '#FF6B6B' :
                appointment.type === 'viaje' ? '#4ECDC4' : '#96CEB4'
            }]}>
              <Ionicons
                name={
                  appointment.type === 'cita' ? 'calendar' :
                  appointment.type === 'viaje' ? 'airplane' : 'map'
                }
                size={16}
                color="#fff"
              />
            </View>
            <View style={styles.locationInfo}>
              <Text style={styles.locationTitle}>{appointment.title}</Text>
              <Text style={styles.locationAddress}>{appointment.location}</Text>
              <Text style={styles.locationDistance}>
                {getDistanceToLocation(appointment.coordinates!)}
              </Text>
            </View>
            <Ionicons name="navigate" size={20} color="#4A90E2" />
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Mostrar loading mientras se verifica autenticación
  if (authLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <StatusBar style="light" />
        <ActivityIndicator size="large" color="#4A90E2" />
        <Text style={styles.loadingText}>Cargando...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {currentScreen === 'login' && renderLoginScreen()}
      {currentScreen === 'register' && renderRegisterScreen()}
      {currentScreen === 'home' && renderHomeScreen()}
      {currentScreen === 'appointments' && renderAppointmentsScreen()}
      {currentScreen === 'map' && renderMapScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    padding: 20,
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 60,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 20,
    textAlign: 'center',
  },
  appSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 8,
  },
  loginForm: {
    marginBottom: 40,
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#fff',
  },
  backButton: {
    position: 'absolute',
    top: -40,
    left: 0,
    padding: 8,
  },
  loginButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  registerButton: {
    borderWidth: 2,
    borderColor: '#fff',
    paddingVertical: 16,
    borderRadius: 12,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  featuresContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  appointmentPreview: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  appointmentInfo: {
    flex: 1,
  },
  appointmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  appointmentDetails: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
    marginBottom: 4,
  },
  appointmentLocation: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.7,
  },
  appointmentType: {
    marginLeft: 12,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  featureText: {
    fontSize: 14,
    color: '#fff',
    marginLeft: 8,
    opacity: 0.9,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
  },
  loader: {
    marginVertical: 20,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 20,
  },
  addButton: {
    backgroundColor: '#4A90E2',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 4,
  },
  logoutButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 30,
  },
  actionButton: {
    width: '48%',
    aspectRatio: 1,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginTop: 8,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  appointmentCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  typeIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  appointmentCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  appointmentCardDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  appointmentCardLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  appointmentCardLocationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    flex: 1,
  },
  distanceText: {
    fontSize: 12,
    color: '#4A90E2',
    fontWeight: '600',
  },
  reminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  reminderButtonText: {
    fontSize: 14,
    color: '#4A90E2',
    marginLeft: 6,
    fontWeight: '600',
  },
  bottomNav: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  navButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  navButtonText: {
    fontSize: 12,
    marginTop: 4,
    color: '#666',
  },
  screenHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    paddingTop: 50,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  screenTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  fullAppointmentCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  appointmentMainInfo: {
    flex: 1,
  },
  fullAppointmentTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 16,
    color: '#666',
    marginLeft: 8,
    flex: 1,
  },
  appointmentActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  actionBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  actionBtnText: {
    fontSize: 14,
    color: '#4A90E2',
    marginLeft: 6,
    fontWeight: '600',
  },
  mapContainer: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f8f8',
  },
  mapPlaceholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ccc',
    marginTop: 16,
  },
  mapPlaceholderSubtext: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
  },
  mapInfo: {
    backgroundColor: '#fff',
    padding: 20,
    maxHeight: 300,
  },
  mapInfoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  mapLocationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  locationMarker: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationInfo: {
    flex: 1,
  },
  locationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  locationDistance: {
    fontSize: 12,
    color: '#4A90E2',
    fontWeight: '600',
  },
});

// Configuración de MongoDB para Recordar Citas
export const DATABASE_CONFIG = {
  // MongoDB Atlas Connection String
  // Reemplaza con tu propia cadena de conexión de MongoDB Atlas
  MONGODB_URI: process.env.EXPO_PUBLIC_MONGODB_URI || 'mongodb+srv://recordar-citas:<EMAIL>/recordar-citas?retryWrites=true&w=majority',
  
  // Nombre de la base de datos
  DATABASE_NAME: 'recordar-citas',
  
  // Nombres de las colecciones
  COLLECTIONS: {
    USERS: 'users',
    APPOINTMENTS: 'appointments',
    SETTINGS: 'settings'
  },
  
  // Configuración de la API (si usas un backend personalizado)
  API_CONFIG: {
    BASE_URL: process.env.EXPO_PUBLIC_API_URL || 'https://tu-api.herokuapp.com',
    TIMEOUT: 10000, // 10 segundos
    RETRY_ATTEMPTS: 3
  },
  
  // Configuración de almacenamiento local
  LOCAL_STORAGE: {
    PREFIX: 'recordar_citas_',
    ENCRYPTION_KEY: 'recordar-citas-secret-key-2025'
  }
};

// Instrucciones para configurar MongoDB Atlas
export const SETUP_INSTRUCTIONS = `
🚀 CONFIGURACIÓN DE MONGODB ATLAS

Para conectar tu aplicación a MongoDB Atlas:

1. 📝 CREAR CUENTA EN MONGODB ATLAS:
   • Ve a https://www.mongodb.com/atlas
   • Crea una cuenta gratuita
   • Crea un nuevo cluster (gratis hasta 512MB)

2. 🔧 CONFIGURAR BASE DE DATOS:
   • Nombre del cluster: recordar-citas
   • Región: Elige la más cercana a ti
   • Tier: M0 Sandbox (gratis)

3. 🔐 CONFIGURAR ACCESO:
   • Database Access → Add New Database User
   • Username: recordar-citas
   • Password: recordar123 (o la que prefieras)
   • Roles: Read and write to any database

4. 🌐 CONFIGURAR RED:
   • Network Access → Add IP Address
   • Selecciona "Allow access from anywhere" (0.0.0.0/0)
   • O agrega tu IP específica para mayor seguridad

5. 🔗 OBTENER CADENA DE CONEXIÓN:
   • Clusters → Connect → Connect your application
   • Driver: Node.js
   • Version: 4.1 or later
   • Copia la connection string

6. ⚙️ CONFIGURAR EN LA APP:
   • Reemplaza MONGODB_URI en este archivo
   • O crea un archivo .env con EXPO_PUBLIC_MONGODB_URI

EJEMPLO DE CONNECTION STRING:
mongodb+srv://recordar-citas:<password>@cluster0.xxxxx.mongodb.net/recordar-citas?retryWrites=true&w=majority

🔄 ALTERNATIVA - BACKEND PERSONALIZADO:
Si prefieres usar tu propio backend:
1. Crea una API REST con Node.js/Express
2. Conecta tu API a MongoDB
3. Actualiza API_CONFIG.BASE_URL
4. Implementa endpoints: /users, /appointments, /auth

📱 MODO OFFLINE:
La app funciona completamente offline usando AsyncStorage.
Los datos se sincronizan automáticamente cuando hay conexión.

🔒 SEGURIDAD:
• Las contraseñas se hashean con SHA256
• Los datos se almacenan de forma segura
• Soporte para autenticación JWT (en backend personalizado)
`;

// Función para validar la configuración
export const validateDatabaseConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (!DATABASE_CONFIG.MONGODB_URI || DATABASE_CONFIG.MONGODB_URI.includes('your-connection-string')) {
    errors.push('❌ MongoDB URI no configurada correctamente');
  }
  
  if (!DATABASE_CONFIG.DATABASE_NAME) {
    errors.push('❌ Nombre de base de datos no especificado');
  }
  
  if (!DATABASE_CONFIG.COLLECTIONS.USERS || !DATABASE_CONFIG.COLLECTIONS.APPOINTMENTS) {
    errors.push('❌ Nombres de colecciones no configurados');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Función para mostrar el estado de la configuración
export const getDatabaseStatus = (): string => {
  const validation = validateDatabaseConfig();
  
  if (validation.isValid) {
    return `
✅ CONFIGURACIÓN VÁLIDA

🔗 MongoDB URI: Configurada
📊 Base de datos: ${DATABASE_CONFIG.DATABASE_NAME}
📁 Colecciones: ${Object.values(DATABASE_CONFIG.COLLECTIONS).join(', ')}
🌐 API URL: ${DATABASE_CONFIG.API_CONFIG.BASE_URL}
💾 Almacenamiento local: Activo

🚀 ¡Tu aplicación está lista para usar MongoDB!
`;
  } else {
    return `
⚠️ CONFIGURACIÓN INCOMPLETA

${validation.errors.join('\n')}

📖 Consulta las instrucciones de configuración:
console.log(SETUP_INSTRUCTIONS);

💡 La app funcionará en modo offline hasta que configures MongoDB.
`;
  }
};

// Exportar configuración por defecto
export default DATABASE_CONFIG;

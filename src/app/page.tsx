'use client';

import { useState } from 'react';
import Image from "next/image";

export default function Home() {
  const [showLogin, setShowLogin] = useState(true);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');

  // Datos de ejemplo de citas próximas
  const upcomingAppointments = [
    {
      id: 1,
      title: "Reunión de trabajo",
      date: "2025-01-15",
      time: "10:00 AM",
      location: "Oficina Central"
    },
    {
      id: 2,
      title: "Cita médica",
      date: "2025-01-16",
      time: "3:00 PM",
      location: "Hospital San Juan"
    },
    {
      id: 3,
      title: "Viaje a París",
      date: "2025-01-20",
      time: "8:00 AM",
      location: "Aeropuerto Internacional"
    }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Aquí iría la lógica de autenticación
    console.log(showLogin ? 'Login' : 'Register', { email, password, name });
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Imagen de fondo */}
      <div className="absolute inset-0 z-0">
        <div className="w-full h-full bg-cover bg-center bg-no-repeat travel-background" />
      </div>

      {/* Contenido principal */}
      <div className="relative z-10 min-h-screen flex">
        {/* Panel izquierdo - Formulario */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-2xl p-8 w-full max-w-md">
            {/* Logo y título */}
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="bg-blue-600 rounded-full p-3">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                </div>
              </div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Recordar Citas</h1>
              <p className="text-gray-600">Tu compañero de viajes y citas</p>
            </div>

            {/* Formulario */}
            <form onSubmit={handleSubmit} className="space-y-6">
              {!showLogin && (
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Nombre completo
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    placeholder="Tu nombre completo"
                    required
                  />
                </div>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Correo electrónico
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Contraseña
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                  placeholder="••••••••"
                  required
                />
              </div>

              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                {showLogin ? 'Iniciar Sesión' : 'Registrarse'}
              </button>
            </form>

            {/* Toggle entre login y registro */}
            <div className="mt-6 text-center">
              <p className="text-gray-600">
                {showLogin ? '¿No tienes cuenta?' : '¿Ya tienes cuenta?'}
                <button
                  type="button"
                  onClick={() => setShowLogin(!showLogin)}
                  className="ml-2 text-blue-600 hover:text-blue-700 font-medium"
                >
                  {showLogin ? 'Regístrate' : 'Inicia sesión'}
                </button>
              </p>
            </div>
          </div>
        </div>

        {/* Panel derecho - Información y citas */}
        <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-8">
          <div className="text-white max-w-lg">
            <h2 className="text-4xl font-bold mb-6">
              Organiza tus viajes y citas como nunca antes
            </h2>
            <p className="text-xl mb-8 text-gray-200">
              Con GPS integrado, recordatorios inteligentes y una interfaz diseñada para móviles.
            </p>

            {/* Próximas citas */}
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
              <h3 className="text-xl font-semibold mb-4 flex items-center">
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Próximas Citas
              </h3>
              <div className="space-y-3">
                {upcomingAppointments.map((appointment) => (
                  <div key={appointment.id} className="bg-white/10 rounded-lg p-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{appointment.title}</h4>
                        <p className="text-sm text-gray-300">{appointment.location}</p>
                      </div>
                      <div className="text-right text-sm">
                        <p>{appointment.date}</p>
                        <p className="text-gray-300">{appointment.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Características */}
            <div className="mt-8 grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-3 w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <p className="text-sm">GPS Integrado</p>
              </div>
              <div className="text-center">
                <div className="bg-white/10 rounded-full p-3 w-12 h-12 mx-auto mb-2 flex items-center justify-center">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-6H4v6zM16 3h5v5h-5V3zM4 3h6v6H4V3z" />
                  </svg>
                </div>
                <p className="text-sm">Recordatorios</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

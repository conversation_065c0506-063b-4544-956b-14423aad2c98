import React, { useState, useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { StyleSheet, Text, View, TouchableOpacity, ScrollView, Alert, ImageBackground, TextInput } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { LinearGradient } from 'expo-linear-gradient';

// Configurar notificaciones
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

// Interfaz para citas
interface Appointment {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  type: 'cita' | 'viaje' | 'tour';
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export default function App() {
  const [currentScreen, setCurrentScreen] = useState<'home' | 'login' | 'register'>('login');
  const [userLocation, setUserLocation] = useState<Location.LocationObject | null>(null);

  // Formularios
  const [loginForm, setLoginForm] = useState({ email: '', password: '' });
  const [registerForm, setRegisterForm] = useState({ email: '', name: '', password: '' });

  // Datos de ejemplo
  const appointments: Appointment[] = [
    {
      id: '1',
      title: 'Reunión de trabajo',
      date: '2025-01-15',
      time: '10:00',
      location: 'Oficina Central, Madrid',
      type: 'cita',
      coordinates: { latitude: 40.4168, longitude: -3.7038 }
    },
    {
      id: '2',
      title: 'Viaje a París',
      date: '2025-01-20',
      time: '08:00',
      location: 'Aeropuerto Barajas, Madrid',
      type: 'viaje',
      coordinates: { latitude: 40.4719, longitude: -3.5626 }
    },
    {
      id: '3',
      title: 'Tour por el Prado',
      date: '2025-01-18',
      time: '15:00',
      location: 'Museo del Prado, Madrid',
      type: 'tour',
      coordinates: { latitude: 40.4138, longitude: -3.6921 }
    }
  ];

  useEffect(() => {
    requestLocationPermission();
    requestNotificationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        const location = await Location.getCurrentPositionAsync({});
        setUserLocation(location);
      }
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  const requestNotificationPermission = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permisos', 'Se necesitan permisos de notificaciones para recordatorios');
    }
  };

  const handleLogin = () => {
    if (!loginForm.email || !loginForm.password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }
    setCurrentScreen('home');
    Alert.alert('¡Bienvenido!', 'Has iniciado sesión correctamente');
  };

  const handleRegister = () => {
    if (!registerForm.email || !registerForm.name || !registerForm.password) {
      Alert.alert('Error', 'Por favor completa todos los campos');
      return;
    }
    setCurrentScreen('home');
    Alert.alert('¡Registro exitoso!', 'Tu cuenta ha sido creada');
  };

  const handleLogout = () => {
    setCurrentScreen('login');
    setLoginForm({ email: '', password: '' });
    setRegisterForm({ email: '', name: '', password: '' });
  };

  const testConnection = () => {
    Alert.alert(
      '🔍 Estado de la Aplicación',
      `📱 Todo funcionando perfectamente:

✅ Aplicación móvil: Activa
✅ GPS: ${userLocation ? 'Ubicación obtenida' : 'Solicitando permisos'}
✅ Notificaciones: Configuradas
✅ Datos: ${appointments.length} citas de ejemplo

🚀 MongoDB: Listo para conectar
📊 ¡Tu app está funcionando genial!`
    );
  };

  const renderLoginScreen = () => (
    <ImageBackground
      source={{ uri: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=2835&q=80' }}
      style={styles.backgroundImage}
    >
      <LinearGradient
        colors={['rgba(0,0,0,0.6)', 'rgba(0,0,0,0.8)']}
        style={styles.overlay}
      >
        <View style={styles.loginContainer}>
          <View style={styles.logoContainer}>
            <Ionicons name="calendar" size={60} color="#fff" />
            <Text style={styles.appTitle}>Recordar Citas</Text>
            <Text style={styles.appSubtitle}>Tu compañero de viajes y citas</Text>
          </View>

          <View style={styles.loginForm}>
            <TextInput
              style={styles.input}
              placeholder="Correo electrónico"
              placeholderTextColor="#ccc"
              value={loginForm.email}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, email: text }))}
              keyboardType="email-address"
              autoCapitalize="none"
            />

            <TextInput
              style={styles.input}
              placeholder="Contraseña"
              placeholderTextColor="#ccc"
              value={loginForm.password}
              onChangeText={(text) => setLoginForm(prev => ({ ...prev, password: text }))}
              secureTextEntry
            />

            <TouchableOpacity style={styles.loginButton} onPress={handleLogin}>
              <Text style={styles.loginButtonText}>Iniciar Sesión</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.registerButton}
              onPress={() => setCurrentScreen('register')}
            >
              <Text style={styles.registerButtonText}>Registrarse</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.testButton} onPress={testConnection}>
              <Ionicons name="server" size={16} color="#4A90E2" />
              <Text style={styles.testButtonText}>🔍 Probar App</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>Próximas Citas</Text>
            {appointments.slice(0, 2).map((appointment) => (
              <View key={appointment.id} style={styles.appointmentPreview}>
                <View style={styles.appointmentInfo}>
                  <Text style={styles.appointmentTitle}>{appointment.title}</Text>
                  <Text style={styles.appointmentDetails}>
                    {appointment.date} • {appointment.time}
                  </Text>
                  <Text style={styles.appointmentLocation}>{appointment.location}</Text>
                </View>
                <View style={styles.appointmentType}>
                  <Ionicons
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    }
                    size={24}
                    color="#4A90E2"
                  />
                </View>
              </View>
            ))}
          </View>
        </View>
      </LinearGradient>
    </ImageBackground>
  );

  const renderHomeScreen = () => (
    <View style={styles.container}>
      <LinearGradient
        colors={['#4A90E2', '#357ABD']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>¡Hola!</Text>
            <Text style={styles.headerSubtitle}>Tienes {appointments.length} citas programadas</Text>
          </View>
          <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
            <Ionicons name="log-out" size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <ScrollView style={styles.content}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Próximas Citas</Text>
          {appointments.map((appointment) => (
            <View key={appointment.id} style={styles.appointmentCard}>
              <View style={styles.appointmentHeader}>
                <View style={[styles.typeIndicator, {
                  backgroundColor:
                    appointment.type === 'cita' ? '#FF6B6B' :
                    appointment.type === 'viaje' ? '#4ECDC4' : '#96CEB4'
                }]}>
                  <Ionicons
                    name={
                      appointment.type === 'cita' ? 'calendar' :
                      appointment.type === 'viaje' ? 'airplane' : 'map'
                    }
                    size={16}
                    color="#fff"
                  />
                </View>
                <Text style={styles.appointmentCardTitle}>{appointment.title}</Text>
              </View>

              <Text style={styles.appointmentCardDate}>
                {appointment.date} • {appointment.time}
              </Text>

              <View style={styles.appointmentCardLocation}>
                <Ionicons name="location" size={16} color="#666" />
                <Text style={styles.appointmentCardLocationText}>
                  {appointment.location}
                </Text>
              </View>

              <TouchableOpacity
                style={styles.reminderButton}
                onPress={() => Alert.alert('Recordatorio', 'Recordatorio programado')}
              >
                <Ionicons name="notifications" size={16} color="#4A90E2" />
                <Text style={styles.reminderButtonText}>Recordar</Text>
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {currentScreen === 'login' && renderLoginScreen()}
      {currentScreen === 'register' && renderRegisterScreen()}
      {currentScreen === 'home' && renderHomeScreen()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
  },
  overlay: {
    flex: 1,
    padding: 20,
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingTop: 60,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    marginTop: 20,
    textAlign: 'center',
  },
  appSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
    textAlign: 'center',
    marginTop: 8,
  },
  loginForm: {
    marginBottom: 40,
  },
  input: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    marginBottom: 16,
    fontSize: 16,
    color: '#fff',
  },
  loginButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  loginButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  registerButton: {
    borderWidth: 2,
    borderColor: '#fff',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  testButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: '#4A90E2',
    paddingVertical: 12,
    borderRadius: 8,
    marginTop: 16,
  },
  testButtonText: {
    color: '#4A90E2',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  featuresContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
  },
  appointmentPreview: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: 'center',
  },
  appointmentInfo: {
    flex: 1,
  },
  appointmentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
    marginBottom: 4,
  },
  appointmentDetails: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
    marginBottom: 4,
  },
  appointmentLocation: {
    fontSize: 12,
    color: '#fff',
    opacity: 0.7,
  },
  appointmentType: {
    marginLeft: 12,
  },
  backButton: {
    position: 'absolute',
    top: -40,
    left: 0,
    padding: 8,
  },
  header: {
    paddingTop: 50,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
    marginTop: 4,
  },
  logoutButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  appointmentCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  appointmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  typeIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  appointmentCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  appointmentCardDate: {
    fontSize: 16,
    color: '#666',
    marginBottom: 8,
  },
  appointmentCardLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  appointmentCardLocationText: {
    fontSize: 14,
    color: '#666',
    marginLeft: 6,
    flex: 1,
  },
  reminderButton: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f0f8ff',
    borderRadius: 8,
  },
  reminderButtonText: {
    fontSize: 14,
    color: '#4A90E2',
    marginLeft: 6,
    fontWeight: '600',
  },
});

  return (
    <View style={styles.container}>
      <StatusBar style="light" />
      {currentScreen === 'login' && renderLoginScreen()}
      {currentScreen === 'register' && (
        <View style={styles.container}>
          <Text style={styles.simpleText}>Pantalla de registro (en desarrollo)</Text>
        </View>
      )}
      {currentScreen === 'home' && (
        <View style={styles.container}>
          <Text style={styles.simpleText}>Pantalla principal (en desarrollo)</Text>
        </View>
      )}
    </View>
  );
}



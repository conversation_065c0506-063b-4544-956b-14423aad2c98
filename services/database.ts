import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Crypto from 'expo-crypto';

// Configuración de MongoDB Atlas
const MONGODB_URI = 'mongodb+srv://recordar-citas:<EMAIL>/recordar-citas?retryWrites=true&w=majority';
const API_BASE_URL = 'https://data.mongodb-api.com/app/data-xxxxx/endpoint/data/v1';

// Interfaces para los datos
export interface User {
  _id?: string;
  email: string;
  name: string;
  password: string;
  createdAt: Date;
  lastLogin?: Date;
}

export interface Appointment {
  _id?: string;
  userId: string;
  title: string;
  date: string;
  time: string;
  location: string;
  type: 'cita' | 'viaje' | 'tour';
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  description?: string;
  reminderSet?: boolean;
  completed?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Clase para manejar la base de datos
class DatabaseService {
  private currentUser: User | null = null;
  private isOnline: boolean = true;

  constructor() {
    this.checkConnection();
  }

  // Verificar conexión a internet
  private async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch('https://www.google.com', { 
        method: 'HEAD',
        timeout: 5000 
      });
      this.isOnline = response.ok;
      return this.isOnline;
    } catch (error) {
      this.isOnline = false;
      return false;
    }
  }

  // Generar hash para contraseñas
  private async hashPassword(password: string): Promise<string> {
    return await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      password + 'recordar-citas-salt'
    );
  }

  // Generar ID único
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // Almacenamiento local como fallback
  private async saveToLocal(key: string, data: any): Promise<void> {
    try {
      await AsyncStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving to local storage:', error);
    }
  }

  private async getFromLocal(key: string): Promise<any> {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Error getting from local storage:', error);
      return null;
    }
  }

  // Simulación de API REST para MongoDB (en una app real usarías MongoDB Realm o tu propio backend)
  private async makeAPICall(endpoint: string, method: string = 'GET', data?: any): Promise<any> {
    const isOnline = await this.checkConnection();
    
    if (!isOnline) {
      // Modo offline - usar almacenamiento local
      return this.handleOfflineOperation(endpoint, method, data);
    }

    try {
      // Simulación de llamada a API
      // En una implementación real, aquí harías la llamada a tu backend con MongoDB
      console.log(`API Call: ${method} ${endpoint}`, data);
      
      // Simular respuesta exitosa
      await new Promise(resolve => setTimeout(resolve, 500)); // Simular latencia
      
      return { success: true, data };
    } catch (error) {
      console.error('API Error:', error);
      // Fallback a almacenamiento local
      return this.handleOfflineOperation(endpoint, method, data);
    }
  }

  private async handleOfflineOperation(endpoint: string, method: string, data?: any): Promise<any> {
    const key = endpoint.replace(/\//g, '_');
    
    switch (method) {
      case 'GET':
        return await this.getFromLocal(key);
      case 'POST':
      case 'PUT':
        await this.saveToLocal(key, data);
        return { success: true, data };
      case 'DELETE':
        await AsyncStorage.removeItem(key);
        return { success: true };
      default:
        return { success: false, error: 'Unsupported operation' };
    }
  }

  // Métodos de autenticación
  async registerUser(email: string, name: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      // Verificar si el usuario ya existe
      const existingUsers = await this.getFromLocal('users') || [];
      const userExists = existingUsers.find((u: User) => u.email === email);
      
      if (userExists) {
        return { success: false, error: 'El usuario ya existe' };
      }

      const hashedPassword = await this.hashPassword(password);
      const newUser: User = {
        _id: this.generateId(),
        email,
        name,
        password: hashedPassword,
        createdAt: new Date(),
      };

      // Guardar usuario
      existingUsers.push(newUser);
      await this.saveToLocal('users', existingUsers);
      
      // Intentar sincronizar con la nube
      await this.makeAPICall('/users', 'POST', newUser);

      this.currentUser = newUser;
      await this.saveToLocal('currentUser', newUser);

      return { success: true, user: newUser };
    } catch (error) {
      console.error('Registration error:', error);
      return { success: false, error: 'Error al registrar usuario' };
    }
  }

  async loginUser(email: string, password: string): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const hashedPassword = await this.hashPassword(password);
      
      // Buscar en almacenamiento local
      const users = await this.getFromLocal('users') || [];
      const user = users.find((u: User) => u.email === email && u.password === hashedPassword);

      if (!user) {
        return { success: false, error: 'Credenciales incorrectas' };
      }

      // Actualizar último login
      user.lastLogin = new Date();
      await this.saveToLocal('users', users);
      
      this.currentUser = user;
      await this.saveToLocal('currentUser', user);

      // Intentar sincronizar con la nube
      await this.makeAPICall(`/users/${user._id}`, 'PUT', user);

      return { success: true, user };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: 'Error al iniciar sesión' };
    }
  }

  async logoutUser(): Promise<void> {
    this.currentUser = null;
    await AsyncStorage.removeItem('currentUser');
  }

  async getCurrentUser(): Promise<User | null> {
    if (this.currentUser) {
      return this.currentUser;
    }
    
    const user = await this.getFromLocal('currentUser');
    this.currentUser = user;
    return user;
  }

  // Métodos para citas
  async getAppointments(): Promise<Appointment[]> {
    try {
      const user = await this.getCurrentUser();
      if (!user) return [];

      const appointments = await this.getFromLocal(`appointments_${user._id}`) || [];
      
      // Intentar sincronizar con la nube
      await this.makeAPICall(`/appointments/${user._id}`, 'GET');

      return appointments;
    } catch (error) {
      console.error('Error getting appointments:', error);
      return [];
    }
  }

  async saveAppointment(appointment: Omit<Appointment, '_id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<{ success: boolean; appointment?: Appointment; error?: string }> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        return { success: false, error: 'Usuario no autenticado' };
      }

      const newAppointment: Appointment = {
        _id: this.generateId(),
        userId: user._id!,
        ...appointment,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const appointments = await this.getFromLocal(`appointments_${user._id}`) || [];
      appointments.push(newAppointment);
      await this.saveToLocal(`appointments_${user._id}`, appointments);

      // Intentar sincronizar con la nube
      await this.makeAPICall('/appointments', 'POST', newAppointment);

      return { success: true, appointment: newAppointment };
    } catch (error) {
      console.error('Error saving appointment:', error);
      return { success: false, error: 'Error al guardar la cita' };
    }
  }

  async updateAppointment(appointmentId: string, updates: Partial<Appointment>): Promise<{ success: boolean; error?: string }> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        return { success: false, error: 'Usuario no autenticado' };
      }

      const appointments = await this.getFromLocal(`appointments_${user._id}`) || [];
      const index = appointments.findIndex((a: Appointment) => a._id === appointmentId);
      
      if (index === -1) {
        return { success: false, error: 'Cita no encontrada' };
      }

      appointments[index] = {
        ...appointments[index],
        ...updates,
        updatedAt: new Date(),
      };

      await this.saveToLocal(`appointments_${user._id}`, appointments);

      // Intentar sincronizar con la nube
      await this.makeAPICall(`/appointments/${appointmentId}`, 'PUT', appointments[index]);

      return { success: true };
    } catch (error) {
      console.error('Error updating appointment:', error);
      return { success: false, error: 'Error al actualizar la cita' };
    }
  }

  async deleteAppointment(appointmentId: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = await this.getCurrentUser();
      if (!user) {
        return { success: false, error: 'Usuario no autenticado' };
      }

      const appointments = await this.getFromLocal(`appointments_${user._id}`) || [];
      const filteredAppointments = appointments.filter((a: Appointment) => a._id !== appointmentId);
      
      await this.saveToLocal(`appointments_${user._id}`, filteredAppointments);

      // Intentar sincronizar con la nube
      await this.makeAPICall(`/appointments/${appointmentId}`, 'DELETE');

      return { success: true };
    } catch (error) {
      console.error('Error deleting appointment:', error);
      return { success: false, error: 'Error al eliminar la cita' };
    }
  }

  // Método para sincronizar datos cuando vuelva la conexión
  async syncData(): Promise<void> {
    const isOnline = await this.checkConnection();
    if (!isOnline) return;

    try {
      const user = await this.getCurrentUser();
      if (!user) return;

      // Sincronizar citas
      const localAppointments = await this.getFromLocal(`appointments_${user._id}`) || [];
      for (const appointment of localAppointments) {
        await this.makeAPICall('/appointments', 'POST', appointment);
      }

      console.log('Data synchronized successfully');
    } catch (error) {
      console.error('Sync error:', error);
    }
  }
}

// Exportar instancia singleton
export const databaseService = new DatabaseService();
